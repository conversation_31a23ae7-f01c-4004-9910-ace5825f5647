import React, { useState, useCallback, useRef, useEffect } from 'react';
import { ListTable } from '@visactor/react-vtable';
import { Button, Space } from 'antd';

interface SimpleData {
  id: number;
  name: string;
  age: number;
}

const VTableSimple: React.FC = () => {
  const [tableData] = useState<SimpleData[]>([
    { id: 1, name: '张三', age: 25 },
    { id: 2, name: '李四', age: 30 },
    { id: 3, name: '王五', age: 28 },
    { id: 4, name: '赵六', age: 35 },
    { id: 5, name: '钱七', age: 22 },
  ]);

  const [indeterminate, setIndeterminate] = useState(false);
  const [checked, setChecked] = useState(false);
  const tableRef = useRef<any>(null);

  // 更新表头复选框状态
  const updateHeaderCheckboxState = useCallback(
    (table: any) => {
      if (!table) return;

      try {
        const selectedCount = table.getCheckedRecords
          ? table.getCheckedRecords().length
          : 0;
        const totalCount = table.recordsCount || tableData.length;

        console.log('更新表头状态:', {
          selectedCount,
          totalCount,
        });

        if (selectedCount === 0) {
          setChecked(false);
          setIndeterminate(false);
        } else if (selectedCount === totalCount) {
          setChecked(true);
          setIndeterminate(false);
        } else {
          setChecked(false);
          setIndeterminate(true);
        }
      } catch (error) {
        console.error('更新表头状态失败:', error);
      }
    },
    [tableData.length]
  );

  const columns = [
    {
      field: 'checkbox',
      cellType: 'checkbox' as const,
      headerType: 'checkbox' as const,
      width: 80,
      checked: checked,
      headerStyle: {
        textAlign: 'center' as const,
        indeterminate: indeterminate,
      },
      style: {
        textAlign: 'center' as const,
      },
    },
    {
      field: 'id',
      title: 'ID',
      width: 80,
    },
    {
      field: 'name',
      title: '姓名',
      width: 150,
    },
    {
      field: 'age',
      title: '年龄',
      width: 100,
    },
  ];

  return (
    <div style={{ padding: '20px' }}>
      <h2>VTable 简单 Indeterminate 测试</h2>

      <div style={{ marginBottom: '20px' }}>
        <p>Checked: {checked ? '是' : '否'}</p>
        <p>Indeterminate: {indeterminate ? '是' : '否'}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <Space>
          <Button onClick={() => setChecked(!checked)}>切换 Checked</Button>
          <Button onClick={() => setIndeterminate(!indeterminate)}>
            切换 Indeterminate
          </Button>
        </Space>
      </div>

      <div
        style={{
          width: '400px',
          height: '300px',
        }}
      >
        <ListTable
          ref={tableRef}
          records={tableData}
          columns={columns}
          width={400}
          height={300}
          onCheckboxStateChange={(args: {
            row: number;
            col: number;
            checked: boolean;
          }) => {
            console.log('onCheckboxStateChange:', args);
            if (tableRef.current) {
              updateHeaderCheckboxState(tableRef.current);
            }
          }}
        />
      </div>
    </div>
  );
};

export default VTableSimple;

import React from 'react';
import { Form, Input, InputNumber, Select } from 'antd';
import type { FormInstance } from 'antd';
import type { UserFormData, DataItem } from '../../types/user';

/**
 * 用户表单组件Props
 */
interface UserFormProps {
  form: FormInstance<UserFormData>;
  layout?: 'horizontal' | 'vertical' | 'inline';
  initialValues?: Partial<UserFormData>;
  style?: React.CSSProperties;
}

/**
 * 标签选项
 */
const tagOptions = [
  { label: '开发', value: '开发' },
  { label: '前端', value: '前端' },
  { label: '后端', value: '后端' },
  { label: '设计师', value: '设计师' },
  { label: '运维', value: '运维' },
  { label: '测试', value: '测试' },
  { label: '产品', value: '产品' },
  { label: '运营', value: '运营' },
];

/**
 * 用户表单组件
 * 可复用的用户信息表单，支持添加和编辑模式
 */
export const UserForm: React.FC<UserFormProps> = ({
  form,
  layout = 'vertical',
  initialValues,
  style,
}) => {
  return (
    <Form
      form={form}
      layout={layout}
      initialValues={initialValues}
      style={style}
    >
      <Form.Item
        name='name'
        label='姓名'
        rules={[
          {
            required: true,
            message: '请输入姓名',
          },
          {
            min: 2,
            message: '姓名至少2个字符',
          },
          {
            max: 20,
            message: '姓名不能超过20个字符',
          },
        ]}
      >
        <Input placeholder='请输入姓名' />
      </Form.Item>

      <Form.Item
        name='age'
        label='年龄'
        rules={[
          {
            required: true,
            message: '请输入年龄',
          },
          {
            type: 'number',
            min: 1,
            max: 150,
            message: '年龄必须在1-150之间',
          },
        ]}
      >
        <InputNumber
          min={1}
          max={150}
          style={{ width: '100%' }}
          placeholder='请输入年龄'
        />
      </Form.Item>

      <Form.Item
        name='address'
        label='地址'
        rules={[
          {
            required: true,
            message: '请输入地址',
          },
          {
            min: 5,
            message: '地址至少5个字符',
          },
          {
            max: 100,
            message: '地址不能超过100个字符',
          },
        ]}
      >
        <Input.TextArea
          rows={3}
          placeholder='请输入地址'
          showCount
          maxLength={100}
        />
      </Form.Item>

      <Form.Item
        name='tags'
        label='标签'
        rules={[
          {
            type: 'array',
            max: 5,
            message: '最多选择5个标签',
          },
        ]}
      >
        <Select
          mode='multiple'
          placeholder='请选择标签'
          options={tagOptions}
          maxTagCount='responsive'
          allowClear
        />
      </Form.Item>
    </Form>
  );
};
